#include "Simulation.h"

// Initialize simulation constants
namespace SimulationValues {
	const float WATER_LEVEL_RAW = 50.0f / 100.0f;   // → 50 (Water Level)
	const float WATER_TEMP_RAW = 25.0f / 80.0f;    // → 25°C (Water Temp)
	const float WATER_PRESS_RAW = 3.0f / 10.0f;    // → 3 (Water Pressure)
	const float ELEC_VOLTAGE = 0.0f / 15.0f;    // → 0V (Electrolyzer Voltage)
	const float ELEC_CURRENT_RAW = 0.0f / 55.0f;    // → 0A (Electrolyzer Current)
	const float ELECTROLIZOR_TEMP_RAW = 30.0f / 80.0f;    // → 30°C (Electrolyzer Temp)
	const float ELECTROLIZOR_PRESS_RAW = 3.0f / 10.0f;    // → 3 (Electrolyzer Pressure)
	const float H2_TANK_TEMP_RAW = 20.0f / 80.0f;    // → 20°C (H₂ Tank Temp)
	const float H2_TANK_PRESS_RAW = 3.0f / 10.0f;    // → 3 (H₂ Tank Pressure)
	const float O2_TANK_TEMP_RAW = 20.0f / 80.0f;    // → 20°C (O₂ Tank Temp)
	const float O2_TANK_PRESS_RAW = 3.0f / 10.0f;    // → 3 (O₂ Tank Pressure)
	const float FC_VOLTAGE = 0.0f / 25.0f;    // → 0V (Fuel Cell Voltage)
	const float FC_CURRENT_RAW = 0.0f / 20.0f;    // → 0A (Fuel Cell Current)
	const float BMS_VOLTAGE_RAW = 24.0f / 80.0f;    // → 24V (BMS Voltage)
	const float BMS_CURRENT_RAW = 0.0f / 50.0f;    // → 0A (BMS Current)
}

// Global simulation variables
bool simulationMode = SIM_MODE_INITIAL;
float analogSensors[NUM_ANALOG_SENSORS];
float adsSensors[14];
PZEMModel pzemModel[NUM_PZEMS];
DigitalInputSimulation simDigitalInputs;
AnalogInputSimulation simAnalogInputs;
bool simDigitalOutputs[ACTUATOR_PIN_COUNT];

float currentACS_BMS_SimValue = 5.0f;  // BMS OUTPUT current, sim value (A)
float currentACS_EL_SimValue = 10.0f; // Electrolyzer input current, sim value (A)
float currentACS_FC_SimValue = 3.0f;  // Fuel Cell output current, sim value (A)
float volt_EL_SimValue = 12.0f;       // Electrolyzer input Volt, sim value (V)
float volt_BMS_SimValue = 24.5f;      // BMS Volt, sim value (V)
float volt_FC_SimValue = 12.0f;       // Fuel Cell output Volt, sim value (V)

// Initialize simulation values
void initializeSimulation() {
	// Initialize analog sensors with default values
	for (int i = 0; i < NUM_ANALOG_SENSORS; i++) {
		analogSensors[i] = 0.0f;
	}

	// Set specific analog sensor values (reordered to match new AnalogSensors enum)
	analogSensors[ADC_SENSOR_WATER_LEVEL_LS_01] = SimulationValues::WATER_LEVEL_RAW;
	analogSensors[ADC_SENSOR_WATER_TEMP_TT_01] = SimulationValues::WATER_TEMP_RAW;
	analogSensors[ADC_SENSOR_WATER_PRESS_PT_01] = SimulationValues::WATER_PRESS_RAW;
	analogSensors[ADC_SENSOR_ELEC_TEMP_TT_02] = SimulationValues::ELECTROLIZOR_TEMP_RAW;
	analogSensors[ADC_SENSOR_H2_TANK_PRESS_PT_02] = SimulationValues::H2_TANK_PRESS_RAW;
	analogSensors[ADC_SENSOR_H2_TANK_TEMP_TT_03] = SimulationValues::H2_TANK_TEMP_RAW;
	analogSensors[ADC_SENSOR_O2_TANK_PRESS_PT_03] = SimulationValues::O2_TANK_PRESS_RAW;
	analogSensors[ADC_SENSOR_O2_TANK_TEMP_TT_04] = SimulationValues::O2_TANK_TEMP_RAW;
	analogSensors[ADC_FC_VOLTAGE_VFC] = SimulationValues::FC_VOLTAGE;
	analogSensors[ADC_SENSOR_BMS_VOLTAGE_VBMS] = SimulationValues::BMS_VOLTAGE_RAW;
	analogSensors[ADC_SENSOR_ELEC_VOLTAGE_VEL] = SimulationValues::ELEC_VOLTAGE;
	analogSensors[ADC_FC_CURRENT_CFC] = SimulationValues::FC_CURRENT_RAW;
	analogSensors[ADC_SENSOR_BMS_CURRENT_CBMS] = SimulationValues::BMS_CURRENT_RAW;
	analogSensors[ADC_SENSOR_ELEC_CURRENT_CEL] = SimulationValues::ELEC_CURRENT_RAW;

	// Copy the same values to ADS sensors
	for (int i = 0; i < 14; i++) {
		adsSensors[i] = (i < NUM_ANALOG_SENSORS) ? analogSensors[i] : 0.0f;
	}

	// Initialize PZEM models
	for (int i = 0; i < NUM_PZEMS; i++) {
		pzemModel[i].voltage = 0.0f;
		pzemModel[i].current = 0.0f;
		pzemModel[i].power = 0.0f;
		pzemModel[i].energy = 0.0f;
		pzemModel[i].frequency = 50.0f;
		pzemModel[i].pf = 1.0f;
	}

	// Initialize analog level switch inputs (LS-02, LS-03, LS-04)
	simAnalogInputs.elecO2DryerLevel = 100;  // High level (needs discharge)
	simAnalogInputs.elecH2DryerLevel = 100;  // High level (needs discharge)
	simAnalogInputs.fcO2DryerLevel = 100;    // High level (needs discharge)

	// Initialize digital inputs (safety sensors)
	simDigitalInputs.sensorFireDetect = false;    // No fire detected
	simDigitalInputs.sensorH2Ambient = false;     // No H2 leak detected
	simDigitalInputs.buttonEmergencyStop = false; // Emergency stop not pressed

	// Initialize digital outputs (all actuators off)
	for (int i = 0; i < ACTUATOR_PIN_COUNT; i++) {
		simDigitalOutputs[i] = false;
	}
}
