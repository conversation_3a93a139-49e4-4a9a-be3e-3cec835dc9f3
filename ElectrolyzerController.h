#ifndef ELECTROLYZER_CONTROLLER_H
#define ELECT<PERSON>LYZER_CONTROLLER_H

#include <Arduino.h>
#include "Constants.h"
#include "Sensors.h"
#include "Utilities.h"

class ElectrolyzerController {
public:
	enum State {
		IDLE,
		PRS1_WATER_CHECK,
		PRS1_POWER_CHECK,
		PRS1_GAS_CHECK,
		PRS2_RUN,
		PRS3_SHUTDOWN_DELAY,
		PRS3_SHUTDOWN_OFF,
		STOPPED,
		EMERGENCY
	};

	ElectrolyzerController();
	void begin();
	void update();
	void reset();
	void emergency_stop();

	State getState() const { return _state; }

private:
	// sensor readings
	
	/// <summary>
	/// MAIN WATER TANK LEVEL SENSOR
	/// </summary>
	int _lvl_LS01;

	/// <summary>
	/// O2 SEPARATOR LEVEL SWITCH
	/// </summary>
	int _lvl_LS02;

	/// <summary>
	/// H2 SEPARATOR LEVEL SWITCH
	/// </summary>
	int _lvl_LS03;

	/// <summary>
	/// WATER PRESSURE
	/// </summary>
	float _press_PT01;

	/// <summary>
	/// H2 TANK
	/// </summary>
	float _press_PT02;

	/// <summary>
	/// O2 TANK
	/// </summary>
	float _press_PT03;

	/// <summary>
	/// WATER TEMP
	/// </summary>
	float _temp_TT01;

	/// <summary>
	/// ELEC TEMP
	/// </summary>
	float _temp_TT02;
	
	float _volt_EL;
	float _curr_EL;

	State _state;
	unsigned long _tState;
	unsigned long _tLastSwitch;
	unsigned long _tHeaterOn;
	bool _heaterRunning;
	int _seqStep;

	void readAllSensors();
	void performPRS1();
	void performPRS2();
	void performPRS3();
	void handleWasteTank();
	void cutELPSUBusPower();

	const char* stateToString(State s) const;
	const char* actuatorStatusJson() const;
};

extern ElectrolyzerController electrolyzer_controller;

#endif // ELECTROLYZER_CONTROLLER_H