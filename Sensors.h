#ifndef SENSORS_H
#define SENSORS_H

/**
 * Sensors.h
 * Handles all sensor operations including reading, filtering, and calibration
 * Manages ADS1115 ADCs, PZEM power monitors, and digital I/O
 */

#include <Arduino.h>
#include <Adafruit_ADS1X15.h>
#include <PZEM004Tv30.h>
#include "Constants.h"
#include "Simulation.h"
#include "VoltageSensor.h"
#include "ACS712Meter.h"
#include "ELRegulator.h"

 /**
  * Sensor calibration structure
  * Used to convert raw sensor values to calibrated readings
  */
struct SensorCalibration {
	float offset;
	float scale;
};

// Calibration data structure
struct CalibrationData {
	uint32_t version;
	SensorCalibration sensors[CAL_SENSOR_COUNT];
	uint32_t magic;
};

// Moving Average Filter class
class MovingAverage {
private:
	static const uint8_t SIZE = 1;
	float readings[SIZE];
	float sum;
	uint8_t index, count;

public:
	MovingAverage();
	float update(float newValue);
	void init(float value);
};

// Predictive Buffer class
class PredictiveBuffer {
public:
	float history[PREDICTIVE_SAMPLES];
	uint8_t index;
	uint8_t count;

	PredictiveBuffer();
	void addSample(float sample);
};

// ADS1115 Reader structure
struct ADS1115Reader {
	Adafruit_ADS1115* ads;
	uint8_t alertPin;
	int16_t readings[4];
	uint8_t currentChannel;
};

// Sensor mapping structure
struct SensorMapping {
	Adafruit_ADS1115* module;
	uint8_t channel;
	uint8_t index;
};

// Global variables
extern CalibrationData calibration_data;
extern const CalibrationData default_calibration;
extern MovingAverage sensorFilters[CAL_SENSOR_COUNT];
extern PredictiveBuffer waterTempBuffer, elecTempBuffer, bmsTempBuffer;
extern Adafruit_ADS1115 ads1, ads2, ads3, ads4;
extern ADS1115Reader adsReaders[4];
extern const SensorMapping sensorMap[14];
extern PZEM004Tv30 pzems[NUM_PZEMS];
extern ACS712Meter _currentSensorBMS;
extern ACS712Meter _currentSensorFC;

extern VoltageSensor _voltageSensorBMS;
extern VoltageSensor _voltageSensorFC;

// Voltage divider configuration (R1=10 kohm, R2=30 kohm, 5V reference)
// Pin definitions are now in Constants.h
static constexpr float VOLTAGE_DIV_R1_BMS = 100000.0f;
static constexpr float VOLTAGE_DIV_R2_BMS = 60000.0f;
static constexpr float ADC_REF_VOLTAGE_BMS = 5.0f;

static constexpr float VOLTAGE_DIV_R1_FC = 100000.0f;
static constexpr float VOLTAGE_DIV_R2_FC = 30000.0f;
static constexpr float ADC_REF_VOLTAGE_FC = 5.0f;

/**
 * Sensor Functions
 */
 // Initialization and update functions
void initializeSensors();  // Initialize all sensors
void updateADSReadings();  // Update ADS1115 readings

// Sensor reading functions
float getFilteredCalibratedValueADS(uint8_t sensor, uint8_t calIndex);  // Get filtered ADS reading
float getFilteredCalibratedValue(uint8_t analogPin, uint8_t calIndex);  // Get filtered analog reading
float calculate_slope(const float data[], uint8_t length, float dt);    // Calculate rate of change

// PZEM power monitor functions
float getPZEMVoltage(uint8_t i);    // Get PZEM voltage
float getPZEMCurrent(uint8_t i);    // Get PZEM current
float getPZEMPower(uint8_t i);      // Get PZEM power
float getPZEMEnergy(uint8_t i);     // Get PZEM energy
float getPZEMFrequency(uint8_t i);  // Get PZEM frequency
float getPZEMPF(uint8_t i);         // Get PZEM power factor

// ACS712 current & voltage sensor functions
float getCurrent_EL();  // returns EL channel current (A)
float getACSCurrent_FC();  // returns FC channel current (A)
float getACSCurrent_BMS();  // returns BMS channel current (A)
float getVolt_EL();        // returns EL channel voltage (V)
float getVolt_FC();        // returns FC channel voltage (V)

bool IsPowerRelayPin(uint8_t pin);

// Digital I/O functions
void SetDigitalOutputVal(uint8_t pin, uint8_t value);  // Set digital output
uint8_t GetDigitalOutputVal(uint8_t pin);               // Get digital output
uint8_t GetLevelSwitchInputVal(uint8_t pin);			// Get LevelSwitchInputVal
uint8_t GetDigitalInputVal(uint8_t pin);               // Get digital input

// Calibration functions
void loadCalibrationFromEEPROM();  // Load calibration data
void saveCalibrationToEEPROM();    // Save calibration data

#endif  // SENSORS_H
